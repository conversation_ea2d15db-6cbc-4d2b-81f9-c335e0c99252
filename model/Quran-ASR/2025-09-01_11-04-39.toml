[meta]
name = "Quran-ASR"
pretrained_path = "elgeish/wav2vec2-large-xlsr-53-arabic"
seed = 42
epochs = 1000
save_dir = "/al_siraat/asr_fine_tuning/ASR-Wav2vec-Finetune/model/"
gradient_accumulation_steps = 2
use_amp = true
device_ids = "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15"
sr = 16000
max_clip_grad_norm = 5.0

[special_tokens]
bos_token = "<bos>"
eos_token = "<eos>"
unk_token = "<unk>"
pad_token = "<pad>"

[huggingface]
push_to_hub = false
push_every_validation_step = false
overwrite_output_dir = false
blocking = false

[train_dataset]
path = "base.base_dataset.BaseDataset"

[val_dataset]
path = "base.base_dataset.BaseDataset"

[optimizer]
lr = 1e-6

[scheduler]
max_lr = 0.0005

[trainer]
path = "trainer.trainer.Trainer"

[huggingface.args]
local_dir = "E:/Quran Trained/10_full_quran/huggingface-hub"
use_auth_token = true
clone_from = ""

[train_dataset.args]
path = "/al_siraat/asr_fine_tuning/ASR-Wav2vec-Finetune/Parah-30_all_qari/train.txt"
preload_data = false
delimiter = "|"
nb_workers = 16

[train_dataset.dataloader]
batch_size = 2
num_workers = 16
pin_memory = true
drop_last = true

[train_dataset.sampler]
shuffle = true
drop_last = true

[val_dataset.args]
path = "/al_siraat/asr_fine_tuning/ASR-Wav2vec-Finetune/Parah-30_all_qari/test.txt"
preload_data = false
delimiter = "|"
nb_workers = 16

[val_dataset.dataloader]
batch_size = 1
num_workers = 4

[val_dataset.sampler]
shuffle = false
drop_last = false

[trainer.args]
validation_interval = 5000
save_max_metric_score = false
