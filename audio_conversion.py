import os
from pydub import AudioSegment

# Set the input and output directories
input_dir = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/audio_data"
output_dir = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/Quran Data/audio_data_wav"

# Create the output directory if it doesn't exist
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# Iterate over all subdirectories in the input directory
for subdir, dirs, files in os.walk(input_dir):
    # Create a corresponding subdirectory in the output directory
    output_subdir = os.path.join(output_dir, os.path.relpath(subdir, input_dir))
    if not os.path.exists(output_subdir):
        os.makedirs(output_subdir)

    # Iterate over all files in the subdirectory
    for filename in files:
        # Check if the file is an MP3 file
        if filename.endswith(".mp3"):
            # Construct the full input and output paths
            input_path = os.path.join(subdir, filename)
            output_path = os.path.join(output_subdir, filename[:-4] + ".wav")

            # Load the MP3 file
            audio = AudioSegment.from_mp3(input_path)

            # Resample the audio to 16000 Hz
            audio = audio.set_frame_rate(16000)

            # Export the audio as a WAV file
            audio.export(output_path, format="wav")

print("Conversion and resampling complete.")





# import tempfile
# from moviepy.editor import VideoFileClip
# from pydub import AudioSegment
# from pydub.silence import split_on_silence
# import glob
# import os

# def convert_mp4_to_audio_segment(input_file):
#     # Load the video file
#     video_clip = VideoFileClip(input_file)

#     # Extract the audio
#     audio_clip = video_clip.audio

#     # Create a temporary file with .wav extension
#     with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
#         # Write the audio file in .wav format
#         audio_clip.write_audiofile(temp_file.name, codec='pcm_s16le')

#         # Convert the audio clip to AudioSegment
#         audio_segment = AudioSegment.from_wav(temp_file.name)

#     # Close the clips
#     audio_clip.close()
#     video_clip.close()

#     # Remove the temporary file
#     os.remove(temp_file.name)

#     return audio_segment

# def split_audio_on_silence(audio_segment, output_folder, min_silence_len=1000, silence_thresh=-40, keep_silence=0):
#     # Split the audio based on silence
#     chunks = split_on_silence(
#         audio_segment,
#         min_silence_len=min_silence_len,
#         silence_thresh=silence_thresh,
#         keep_silence=keep_silence
#     )

#     # Ensure the output folder exists
#     os.makedirs(output_folder, exist_ok=True)

#     # Export each chunk as a separate audio file
#     for i, chunk in enumerate(chunks):
#         chunk_file = os.path.join(output_folder, f"chunk_{i+1}.wav")
#         chunk.export(chunk_file, format="wav")
#         print(f"Exported {chunk_file}")

# def process_directory(input_directory):
#     # Ensure the output folder exists
#     os.makedirs(input_directory, exist_ok=True)

#     # Iterate over all mp4 files in the input directory
#     for input_file in glob.glob(os.path.join(input_directory, "*.mp4")):
#         # Get the base name of the input file (without extension)
#         base_name = os.path.basename(input_file)
#         base_name_no_ext = os.path.splitext(base_name)[0]

#         # Create a separate output folder for each input file
#         output_folder = os.path.join(input_directory, base_name_no_ext)
#         os.makedirs(output_folder, exist_ok=True)

#         # Call the conversion function
#         audio_segment = convert_mp4_to_audio_segment(input_file)

#         # Call the split audio function
#         split_audio_on_silence(audio_segment, output_folder)

# # Specify the input directory path
# input_directory = "D:/Quran Learning/Quran Speech Dataset/archive/Quran_Ayat_public/audio_data_train/000Surah Shams/New folder"

# # Call the process_directory function
# process_directory(input_directory)
